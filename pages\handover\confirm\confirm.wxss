/* pages/handover/confirm/confirm.wxss */
.confirm-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.confirm-content {
  padding-bottom: 120rpx;
}

/* 交班信息卡片 */
.shift-info-card {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 24rpx;
  color: #969799;
}

.info-value {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
}

.special-notes {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 6rpx solid #1989fa;
}

.notes-label {
  font-size: 26rpx;
  color: #646566;
  font-weight: 600;
}

.notes-content {
  font-size: 28rpx;
  color: #323233;
  line-height: 1.6;
  margin-left: 8rpx;
}

/* 检查区域 */
.check-section,
.issue-section,
.photo-section,
.notes-section,
.signature-section {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
}

.required-mark {
  color: #ee0a24;
  font-size: 28rpx;
}

.add-issue-btn,
.add-photo-btn {
  height: 56rpx;
  font-size: 24rpx;
}

/* 器械检查 */
.instrument-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.instrument-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.instrument-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.instrument-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.count-input-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.count-label {
  font-size: 26rpx;
  color: #646566;
}

.count-input {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.input-label {
  font-size: 26rpx;
  color: #646566;
}

/* 设备检查 */
.equipment-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.equipment-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.equipment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.equipment-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.expected-status {
  font-size: 24rpx;
  color: #646566;
}

.status-select {
  margin-bottom: 16rpx;
}

.select-label {
  font-size: 26rpx;
  color: #646566;
  margin-bottom: 12rpx;
  display: block;
}

/* 问题记录 */
.issue-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.issue-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.issue-type {
  font-size: 26rpx;
  color: #646566;
  flex: 1;
}

.issue-description {
  font-size: 28rpx;
  color: #323233;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.issue-photos {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.issue-photo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.empty-issue,
.empty-photos {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 60rpx 40rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #969799;
}

/* 照片网格 */
.photo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.photo-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ee0a24;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 电子签名 */
.signature-area {
  border: 2rpx dashed #dcdee0;
  border-radius: 12rpx;
  min-height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.signature-image {
  max-width: 100%;
  max-height: 200rpx;
}

.signature-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.placeholder-text {
  font-size: 26rpx;
  color: #969799;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx;
  border-top: 2rpx solid #ebedf0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.reject-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  font-weight: 600;
}

/* 弹窗样式 */
.issue-popup {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.popup-content {
  flex: 1;
  padding: 32rpx;
}

.form-section {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
  display: block;
}

.issue-photo-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

.issue-photo-item {
  position: relative;
  aspect-ratio: 1;
}

.issue-photo-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.issue-photo-delete {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 24rpx;
  height: 24rpx;
  background: #ee0a24;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-issue-photo {
  aspect-ratio: 1;
  border: 2rpx dashed #dcdee0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  padding: 32rpx;
  border-top: 2rpx solid #ebedf0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .photo-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .issue-photo-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
