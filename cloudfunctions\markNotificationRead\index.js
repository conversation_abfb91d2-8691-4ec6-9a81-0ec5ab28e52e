// 云函数：标记通知已读
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { notificationId, userId, markAll = false } = event

  try {
    if (!userId) {
      return {
        success: false,
        message: '用户ID不能为空'
      }
    }

    if (markAll) {
      // 标记所有未读通知为已读
      const result = await db.collection('notifications')
        .where({
          recipients: _.in([userId]),
          status: 'pending'
        })
        .update({
          data: {
            status: 'read',
            readTime: new Date().toISOString()
          }
        })

      return {
        success: true,
        message: '所有通知已标记为已读',
        data: {
          updatedCount: result.stats.updated
        }
      }
    } else {
      // 标记单个通知为已读
      if (!notificationId) {
        return {
          success: false,
          message: '通知ID不能为空'
        }
      }

      // 检查通知是否存在且用户有权限
      const notification = await db.collection('notifications')
        .doc(notificationId)
        .get()

      if (!notification.data) {
        return {
          success: false,
          message: '通知不存在'
        }
      }

      if (!notification.data.recipients.includes(userId)) {
        return {
          success: false,
          message: '无权限访问此通知'
        }
      }

      // 更新通知状态
      await db.collection('notifications')
        .doc(notificationId)
        .update({
          data: {
            status: 'read',
            readTime: new Date().toISOString()
          }
        })

      return {
        success: true,
        message: '通知已标记为已读'
      }
    }
  } catch (error) {
    console.error('标记通知已读失败:', error)
    return {
      success: false,
      message: '标记通知已读失败',
      error: error.message
    }
  }
}
