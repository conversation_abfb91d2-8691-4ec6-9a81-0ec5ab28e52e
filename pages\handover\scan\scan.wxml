<!--pages/handover/scan/scan.wxml-->
<view class="scan-container">
  <!-- 顶部导航 -->
  <view class="scan-header">
    <van-nav-bar
      title="扫码接班"
      left-arrow
      bind:click-left="goBack"
      custom-style="background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);"
    >
      <view slot="right" class="header-actions">
        <van-icon name="clock-o" size="20px" bindtap="showScanHistory" />
        <van-icon name="edit" size="20px" bindtap="showManualInputDialog" />
      </view>
    </van-nav-bar>
  </view>

  <!-- 扫码区域 -->
  <view class="scan-content">
    <view class="scan-area">
      <view class="scan-frame">
        <view class="scan-corner scan-corner-tl"></view>
        <view class="scan-corner scan-corner-tr"></view>
        <view class="scan-corner scan-corner-bl"></view>
        <view class="scan-corner scan-corner-br"></view>
        <view class="scan-line"></view>
      </view>

      <view class="scan-tips">
        <text class="tips-text">将二维码放入框内，即可自动扫描</text>
      </view>
    </view>

    <!-- 功能按钮 -->
    <view class="scan-actions">
      <van-button
        type="primary"
        size="large"
        round
        loading="{{scanning}}"
        disabled="{{!cameraAuthorized}}"
        bindtap="startScan"
        class="scan-btn">
        <van-icon name="scan" size="20px" />
        <text>{{scanning ? '扫描中...' : '开始扫码'}}</text>
      </van-button>

      <view class="action-row">
        <van-button
          type="default"
          size="small"
          bindtap="showManualInputDialog"
          class="manual-btn">
          <van-icon name="edit" size="16px" />
          <text>手动输入</text>
        </van-button>

        <van-button
          type="default"
          size="small"
          bindtap="showScanHistory"
          class="history-btn">
          <van-icon name="clock-o" size="16px" />
          <text>历史记录</text>
        </van-button>
      </view>
    </view>

    <!-- 权限提示 -->
    <view class="permission-tips" wx:if="{{!cameraAuthorized}}">
      <van-icon name="warning-o" size="24px" color="#ff976a" />
      <text class="tips-text">需要相机权限才能使用扫码功能</text>
      <van-button
        type="primary"
        size="small"
        bindtap="checkCameraPermission"
        class="permission-btn">
        开启权限
      </van-button>
    </view>

    <!-- 扫码结果 -->
    <view class="scan-result" wx:if="{{scanResult}}">
      <view class="result-card">
        <view class="result-header">
          <van-icon name="success" size="20px" color="#07c160" />
          <text class="result-title">扫码成功</text>
        </view>
        <view class="result-content">
          <text class="result-text">{{scanResult}}</text>
        </view>
        <view class="result-actions">
          <van-button
            type="default"
            size="small"
            bindtap="rescan"
            class="rescan-btn">
            重新扫码
          </van-button>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="scan-guide">
    <view class="guide-title">
      <van-icon name="info-o" size="16px" />
      <text>使用说明</text>
    </view>
    <view class="guide-content">
      <view class="guide-item">
        <text class="guide-number">1</text>
        <text class="guide-text">扫描交班单上的二维码进行接班</text>
      </view>
      <view class="guide-item">
        <text class="guide-number">2</text>
        <text class="guide-text">扫描设备二维码查看设备信息</text>
      </view>
      <view class="guide-item">
        <text class="guide-number">3</text>
        <text class="guide-text">可手动输入二维码内容</text>
      </view>
    </view>
  </view>
</view>

<!-- 手动输入弹窗 -->
<van-popup
  show="{{showManualInput}}"
  position="center"
  round
  bind:close="closeManualInput">
  <view class="manual-input-popup">
    <view class="popup-header">
      <text class="popup-title">手动输入</text>
      <van-icon name="cross" bindtap="closeManualInput" />
    </view>
    <view class="popup-content">
      <van-field
        value="{{manualCode}}"
        placeholder="请输入二维码内容"
        type="textarea"
        autosize
        border="{{false}}"
        bind:change="onManualCodeChange"
        custom-style="background: #f8f9fa; border-radius: 8rpx;"
      />
    </view>
    <view class="popup-actions">
      <van-button
        type="default"
        size="large"
        bindtap="closeManualInput"
        class="cancel-btn">
        取消
      </van-button>
      <van-button
        type="primary"
        size="large"
        bindtap="confirmManualInput"
        class="confirm-btn">
        确定
      </van-button>
    </view>
  </view>
</van-popup>

<!-- 历史记录弹窗 -->
<van-popup
  show="{{showHistory}}"
  position="bottom"
  round
  bind:close="closeScanHistory"
  custom-style="height: 60%;">
  <view class="history-popup">
    <view class="popup-header">
      <text class="popup-title">扫码历史</text>
      <view class="header-actions">
        <van-icon name="delete-o" size="18px" bindtap="clearScanHistory" />
        <van-icon name="cross" size="18px" bindtap="closeScanHistory" />
      </view>
    </view>
    <scroll-view class="history-list" scroll-y>
      <view
        class="history-item"
        wx:for="{{scanHistory}}"
        wx:key="timestamp"
        data-index="{{index}}"
        bindtap="selectHistoryItem">
        <view class="history-content">
          <text class="history-text">{{item.content}}</text>
          <text class="history-time">{{item.timestamp}}</text>
        </view>
        <van-icon name="arrow" size="16px" />
      </view>

      <view class="empty-state" wx:if="{{scanHistory.length === 0}}">
        <van-icon name="records" size="48px" color="#dcdee0" />
        <text class="empty-text">暂无扫码历史</text>
      </view>
    </scroll-view>
  </view>
</van-popup>

<!-- 全局组件 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
