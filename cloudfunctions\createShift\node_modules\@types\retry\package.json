{"name": "@types/retry", "version": "0.12.5", "description": "TypeScript definitions for retry", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/retry", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "krenor", "url": "https://github.com/krenor"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/retry"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "aa54558b4adab882512b32e89d71a59d1864fa79684a3ef317002d0e144cba11", "typeScriptVersion": "4.5"}