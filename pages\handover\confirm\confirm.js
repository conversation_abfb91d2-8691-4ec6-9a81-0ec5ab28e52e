// pages/handover/confirm/confirm.js
const app = getApp()
const util = require('../../../utils/util.js')
const api = require('../../../utils/api.js')
const config = require('../../../utils/config.js')

Page({
  data: {
    // 交班信息
    shiftDetail: null,
    shiftId: '',

    // 确认表单
    confirmForm: {
      receiverSignature: '',
      confirmNotes: '',
      instrumentCheckResult: [],
      equipmentCheckResult: [],
      issueList: [],
      photos: []
    },

    // 状态
    loading: false,
    submitting: false,

    // 弹窗状态
    showSignaturePopup: false,
    showIssuePopup: false,
    showPhotoPreview: false,
    previewImageUrl: '',

    // 问题相关
    currentIssue: {
      type: '',
      description: '',
      severity: 'normal',
      photos: []
    },
    issueTypes: [
      { text: '器械缺失', value: 'missing' },
      { text: '器械损坏', value: 'damaged' },
      { text: '设备故障', value: 'equipment_fault' },
      { text: '清洁不彻底', value: 'cleaning_issue' },
      { text: '包装问题', value: 'packaging_issue' },
      { text: '其他问题', value: 'other' }
    ],
    severityOptions: [
      { text: '轻微', value: 'minor' },
      { text: '一般', value: 'normal' },
      { text: '严重', value: 'major' },
      { text: '紧急', value: 'urgent' }
    ]
  },

  onLoad(options) {
    const { shiftId } = options
    if (shiftId) {
      this.setData({ shiftId })
      this.loadShiftDetail(shiftId)
    } else {
      util.showToast('缺少交班ID')
      wx.navigateBack()
    }
  },

  // 加载交班详情
  async loadShiftDetail(shiftId) {
    try {
      this.setData({ loading: true })

      const result = await api.callFunction('getShiftDetail', { shiftId })

      if (result.success) {
        const shiftDetail = result.data

        // 检查交班状态
        if (shiftDetail.status === 'confirmed') {
          util.showToast('该交班已被确认')
          wx.navigateBack()
          return
        }

        if (shiftDetail.status === 'cancelled') {
          util.showToast('该交班已被取消')
          wx.navigateBack()
          return
        }

        // 初始化确认表单
        const confirmForm = {
          receiverSignature: '',
          confirmNotes: '',
          instrumentCheckResult: shiftDetail.instrumentCheck.map(item => ({
            ...item,
            actualCount: item.planCount,
            status: 'normal',
            notes: ''
          })),
          equipmentCheckResult: shiftDetail.equipmentStatus.map(item => ({
            ...item,
            actualStatus: item.expectedStatus,
            notes: ''
          })),
          issueList: [],
          photos: []
        }

        this.setData({
          shiftDetail,
          confirmForm
        })
      } else {
        throw new Error(result.message || '获取交班详情失败')
      }
    } catch (error) {
      console.error('加载交班详情失败:', error)
      util.showToast(error.message || '加载失败')
      wx.navigateBack()
    } finally {
      this.setData({ loading: false })
    }
  },

  // 器械数量变化
  onInstrumentCountChange(e) {
    const { index } = e.currentTarget.dataset
    const { value } = e.detail
    const path = `confirmForm.instrumentCheckResult[${index}].actualCount`

    this.setData({ [path]: parseInt(value) || 0 })
    this.updateInstrumentStatus(index)
  },

  // 更新器械状态
  updateInstrumentStatus(index) {
    const item = this.data.confirmForm.instrumentCheckResult[index]
    const status = item.actualCount === item.planCount ? 'normal' : 'abnormal'
    const path = `confirmForm.instrumentCheckResult[${index}].status`

    this.setData({ [path]: status })
  },

  // 器械备注变化
  onInstrumentNotesChange(e) {
    const { index } = e.currentTarget.dataset
    const { value } = e.detail
    const path = `confirmForm.instrumentCheckResult[${index}].notes`

    this.setData({ [path]: value })
  },

  // 设备状态变化
  onEquipmentStatusChange(e) {
    const { index } = e.currentTarget.dataset
    const { value } = e.detail
    const path = `confirmForm.equipmentCheckResult[${index}].actualStatus`

    this.setData({ [path]: value })
  },

  // 设备备注变化
  onEquipmentNotesChange(e) {
    const { index } = e.currentTarget.dataset
    const { value } = e.detail
    const path = `confirmForm.equipmentCheckResult[${index}].notes`

    this.setData({ [path]: value })
  },

  // 确认备注变化
  onConfirmNotesChange(e) {
    this.setData({
      'confirmForm.confirmNotes': e.detail
    })
  },

  // 添加问题
  addIssue() {
    this.setData({
      showIssuePopup: true,
      currentIssue: {
        type: '',
        description: '',
        severity: 'normal',
        photos: []
      }
    })
  },

  // 关闭问题弹窗
  closeIssuePopup() {
    this.setData({ showIssuePopup: false })
  },

  // 问题类型选择
  onIssueTypeChange(e) {
    this.setData({
      'currentIssue.type': e.detail
    })
  },

  // 问题描述变化
  onIssueDescriptionChange(e) {
    this.setData({
      'currentIssue.description': e.detail
    })
  },

  // 问题严重程度选择
  onIssueSeverityChange(e) {
    this.setData({
      'currentIssue.severity': e.detail
    })
  },

  // 为问题添加照片
  addIssuePhoto() {
    wx.chooseMedia({
      count: 3,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const photos = res.tempFiles.map(file => file.tempFilePath)
        const currentPhotos = this.data.currentIssue.photos
        this.setData({
          'currentIssue.photos': [...currentPhotos, ...photos]
        })
      }
    })
  },

  // 删除问题照片
  removeIssuePhoto(e) {
    const { index } = e.currentTarget.dataset
    const photos = this.data.currentIssue.photos
    photos.splice(index, 1)
    this.setData({
      'currentIssue.photos': photos
    })
  },

  // 确认添加问题
  confirmAddIssue() {
    const issue = this.data.currentIssue

    if (!issue.type) {
      util.showToast('请选择问题类型')
      return
    }

    if (!issue.description.trim()) {
      util.showToast('请输入问题描述')
      return
    }

    const issueList = [...this.data.confirmForm.issueList]
    issueList.push({
      ...issue,
      id: Date.now(),
      createTime: new Date().toISOString()
    })

    this.setData({
      'confirmForm.issueList': issueList,
      showIssuePopup: false
    })
  },

  // 删除问题
  removeIssue(e) {
    const { index } = e.currentTarget.dataset
    const issueList = this.data.confirmForm.issueList
    issueList.splice(index, 1)
    this.setData({
      'confirmForm.issueList': issueList
    })
  },

  // 添加照片
  addPhoto() {
    wx.chooseMedia({
      count: 5,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const photos = res.tempFiles.map(file => file.tempFilePath)
        const currentPhotos = this.data.confirmForm.photos
        this.setData({
          'confirmForm.photos': [...currentPhotos, ...photos]
        })
      }
    })
  },

  // 删除照片
  removePhoto(e) {
    const { index } = e.currentTarget.dataset
    const photos = this.data.confirmForm.photos
    photos.splice(index, 1)
    this.setData({
      'confirmForm.photos': photos
    })
  },

  // 预览照片
  previewPhoto(e) {
    const { url } = e.currentTarget.dataset
    wx.previewImage({
      current: url,
      urls: this.data.confirmForm.photos
    })
  },

  // 电子签名
  openSignature() {
    util.navigateTo('/pages/signature/signature', {
      title: '接班确认签名',
      callback: 'handleSignatureResult'
    })
  },

  // 处理签名结果
  handleSignatureResult(signature) {
    this.setData({
      'confirmForm.receiverSignature': signature
    })
  },

  // 验证表单
  validateForm() {
    const { confirmForm } = this.data

    if (!confirmForm.receiverSignature) {
      util.showToast('请完成电子签名')
      return false
    }

    // 检查是否有异常项目但没有添加问题说明
    const hasAbnormalInstrument = confirmForm.instrumentCheckResult.some(
      item => item.status === 'abnormal'
    )
    const hasAbnormalEquipment = confirmForm.equipmentCheckResult.some(
      item => item.actualStatus !== item.expectedStatus
    )

    if ((hasAbnormalInstrument || hasAbnormalEquipment) && confirmForm.issueList.length === 0) {
      util.showConfirm('检测到异常项目，建议添加问题说明，是否继续？').then(confirm => {
        if (confirm) {
          this.submitConfirm()
        }
      })
      return false
    }

    return true
  },

  // 提交确认
  async submitConfirm() {
    try {
      if (!this.validateForm()) {
        return
      }

      this.setData({ submitting: true })

      // 上传照片
      const uploadedPhotos = await this.uploadPhotos()

      // 准备提交数据
      const submitData = {
        shiftId: this.data.shiftId,
        confirmForm: {
          ...this.data.confirmForm,
          photos: uploadedPhotos,
          receiverId: app.globalData.userInfo.userId,
          receiverName: app.globalData.userInfo.name,
          confirmTime: new Date().toISOString()
        }
      }

      // 调用确认接口
      const result = await api.callFunction('confirmHandover', submitData)

      if (result.success) {
        util.showToast('交班确认成功', 'success')

        // 跳转到详情页面
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/shift/detail/detail?id=${this.data.shiftId}`
          })
        }, 1500)
      } else {
        throw new Error(result.message || '确认失败')
      }
    } catch (error) {
      console.error('提交确认失败:', error)
      util.showToast(error.message || '提交失败，请重试')
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 上传照片
  async uploadPhotos() {
    try {
      const photos = this.data.confirmForm.photos
      if (photos.length === 0) return []

      const uploadPromises = photos.map(async (photo, index) => {
        const cloudPath = `handover/${this.data.shiftId}/${Date.now()}_${index}.jpg`
        const result = await api.uploadFile(photo, cloudPath)
        return result.fileID
      })

      return await Promise.all(uploadPromises)
    } catch (error) {
      console.error('上传照片失败:', error)
      throw new Error('上传照片失败')
    }
  },

  // 拒绝交班
  rejectHandover() {
    wx.showModal({
      title: '拒绝交班',
      content: '确定要拒绝此次交班吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ submitting: true })

            const result = await api.callFunction('rejectHandover', {
              shiftId: this.data.shiftId,
              rejectReason: '接班人拒绝',
              rejecterId: app.globalData.userInfo.userId
            })

            if (result.success) {
              util.showToast('已拒绝交班')
              wx.navigateBack()
            } else {
              throw new Error(result.message || '操作失败')
            }
          } catch (error) {
            console.error('拒绝交班失败:', error)
            util.showToast(error.message || '操作失败')
          } finally {
            this.setData({ submitting: false })
          }
        }
      }
    })
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '供应室交班确认',
      path: `/pages/handover/confirm/confirm?shiftId=${this.data.shiftId}`,
      imageUrl: '/images/share-confirm.png'
    }
  }
})
