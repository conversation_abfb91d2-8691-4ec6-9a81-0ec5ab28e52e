// pages/shift/detail/detail.js
const app = getApp()
const util = require('../../../utils/util.js')
const api = require('../../../utils/api.js')

Page({
  data: {
    shiftId: '',
    shiftDetail: null,
    loading: true,
    userInfo: null
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.setData({ 
        shiftId: id,
        userInfo: app.globalData.userInfo 
      })
      this.loadShiftDetail()
    } else {
      util.showToast('参数错误')
      wx.navigateBack()
    }
  },

  // 加载交班详情
  async loadShiftDetail() {
    try {
      this.setData({ loading: true })
      
      const result = await api.callFunction('getShiftDetail', {
        shiftId: this.data.shiftId
      })

      if (result.success) {
        this.setData({ shiftDetail: result.data })
      } else {
        util.showToast('加载失败')
        wx.navigateBack()
      }
    } catch (error) {
      console.error('加载交班详情失败:', error)
      util.showToast('加载失败')
      wx.navigateBack()
    } finally {
      this.setData({ loading: false })
    }
  },

  // 编辑交班
  editShift() {
    wx.navigateTo({
      url: `/pages/shift/create/create?id=${this.data.shiftId}&mode=edit`
    })
  },

  // 生成二维码
  generateQRCode() {
    // 实现二维码生成
    util.showToast('功能开发中')
  },

  // 取消交班
  async cancelShift() {
    try {
      const result = await util.showModal('确认取消', '确定要取消这个交班吗？')
      if (!result.confirm) return

      util.showLoading('处理中...')

      const updateResult = await api.callFunction('updateShift', {
        shiftId: this.data.shiftId,
        status: 'cancelled'
      })

      if (updateResult.success) {
        util.showToast('已取消', 'success')
        this.loadShiftDetail()
      } else {
        util.showToast('操作失败')
      }
    } catch (error) {
      console.error('取消交班失败:', error)
      util.showToast('操作失败')
    } finally {
      util.hideLoading()
    }
  },

  // 接班确认
  confirmHandover() {
    const shiftDetail = this.data.shiftDetail
    if (!shiftDetail) return

    // 跳转到确认页面
    wx.navigateTo({
      url: `/pages/handover/confirm/confirm?shiftId=${this.data.shiftId}&type=shift`
    })
  },

  // 拒绝交班
  async rejectHandover() {
    try {
      const result = await util.showModal('拒绝交班', '请输入拒绝原因', true)
      if (!result.confirm || !result.content) return

      util.showLoading('处理中...')

      const rejectResult = await api.callFunction('rejectHandover', {
        shiftId: this.data.shiftId,
        rejectReason: result.content,
        rejecterId: this.data.userInfo.userId
      })

      if (rejectResult.success) {
        util.showToast('拒绝成功', 'success')
        this.loadShiftDetail()
      } else {
        util.showToast(rejectResult.message || '拒绝失败')
      }
    } catch (error) {
      console.error('拒绝交班失败:', error)
      util.showToast('拒绝失败')
    } finally {
      util.hideLoading()
    }
  },

  // 查看二维码
  showQRCode() {
    const shiftDetail = this.data.shiftDetail
    if (!shiftDetail || !shiftDetail.qrCode) {
      util.showToast('二维码不存在')
      return
    }

    wx.previewImage({
      urls: [shiftDetail.qrCode],
      current: shiftDetail.qrCode
    })
  },

  // 查看附件
  previewAttachment(e) {
    const { url, type } = e.currentTarget.dataset

    if (type === 'image') {
      wx.previewImage({
        urls: [url],
        current: url
      })
    } else {
      // 其他类型文件下载
      wx.downloadFile({
        url: url,
        success: (res) => {
          wx.openDocument({
            filePath: res.tempFilePath
          })
        },
        fail: () => {
          util.showToast('文件打开失败')
        }
      })
    }
  },

  // 预览附件
  previewAttachment(e) {
    const { url, type } = e.currentTarget.dataset
    if (type === 'image') {
      wx.previewImage({
        urls: [url],
        current: url
      })
    } else {
      // 其他类型文件的预览
      wx.downloadFile({
        url: url,
        success: (res) => {
          wx.openDocument({
            filePath: res.tempFilePath
          })
        }
      })
    }
  },

  // 工具函数
  formatTime(time) {
    return util.formatTime(new Date(time))
  },

  getStatusText(status) {
    const statusMap = {
      'pending': '待接班',
      'completed': '已接班',
      'cancelled': '已取消',
      'abnormal': '异常'
    }
    return statusMap[status] || status
  },

  getPriorityText(priority) {
    const priorityMap = {
      'normal': '普通',
      'important': '重要',
      'urgent': '紧急'
    }
    return priorityMap[priority] || priority
  }
})
