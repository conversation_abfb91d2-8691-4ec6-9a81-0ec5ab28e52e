<!--pages/handover/confirm/confirm.wxml-->
<view class="confirm-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" size="24px" vertical>
    加载中...
  </van-loading>

  <!-- 主要内容 -->
  <view wx:else class="confirm-content">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="确认接班"
      left-arrow
      bind:click-left="goBack"
      custom-style="background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);"
    />

    <!-- 交班基本信息 -->
    <view class="shift-info-card">
      <view class="card-header">
        <van-icon name="exchange" size="20px" />
        <text class="card-title">交班信息</text>
        <van-tag type="{{shiftDetail.priority === 'urgent' ? 'danger' : 'primary'}}" size="medium">
          {{shiftDetail.priorityText}}
        </van-tag>
      </view>
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">交班人</text>
          <text class="info-value">{{shiftDetail.creatorName}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">班次类型</text>
          <text class="info-value">{{shiftDetail.shiftType}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">交班日期</text>
          <text class="info-value">{{shiftDetail.shiftDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{shiftDetail.createTime}}</text>
        </view>
      </view>
      <view class="special-notes" wx:if="{{shiftDetail.specialNotes}}">
        <text class="notes-label">特殊说明：</text>
        <text class="notes-content">{{shiftDetail.specialNotes}}</text>
      </view>
    </view>

    <!-- 器械检查 -->
    <view class="check-section">
      <view class="section-header">
        <van-icon name="orders-o" size="18px" />
        <text class="section-title">器械检查</text>
      </view>
      <view class="instrument-list">
        <view
          class="instrument-item"
          wx:for="{{confirmForm.instrumentCheckResult}}"
          wx:key="instrumentId">
          <view class="instrument-header">
            <text class="instrument-name">{{item.instrumentName}}</text>
            <van-tag
              type="{{item.status === 'normal' ? 'success' : 'warning'}}"
              size="small">
              {{item.status === 'normal' ? '正常' : '异常'}}
            </van-tag>
          </view>
          <view class="count-input-row">
            <text class="count-label">计划数量：{{item.planCount}}</text>
            <view class="count-input">
              <text class="input-label">实际数量：</text>
              <van-stepper
                value="{{item.actualCount}}"
                min="0"
                data-index="{{index}}"
                bind:change="onInstrumentCountChange"
              />
            </view>
          </view>
          <van-field
            value="{{item.notes}}"
            placeholder="备注说明（可选）"
            type="textarea"
            autosize
            border="{{false}}"
            data-index="{{index}}"
            bind:change="onInstrumentNotesChange"
            custom-style="background: #f8f9fa; border-radius: 8rpx; margin-top: 16rpx;"
          />
        </view>
      </view>
    </view>

    <!-- 设备检查 -->
    <view class="check-section">
      <view class="section-header">
        <van-icon name="setting-o" size="18px" />
        <text class="section-title">设备检查</text>
      </view>
      <view class="equipment-list">
        <view
          class="equipment-item"
          wx:for="{{confirmForm.equipmentCheckResult}}"
          wx:key="equipmentId">
          <view class="equipment-header">
            <text class="equipment-name">{{item.equipmentName}}</text>
            <text class="expected-status">预期：{{item.expectedStatus}}</text>
          </view>
          <view class="status-select">
            <text class="select-label">实际状态：</text>
            <van-radio-group
              value="{{item.actualStatus}}"
              direction="horizontal"
              data-index="{{index}}"
              bind:change="onEquipmentStatusChange">
              <van-radio name="normal">正常</van-radio>
              <van-radio name="fault">故障</van-radio>
              <van-radio name="repairing">维修中</van-radio>
              <van-radio name="disabled">停用</van-radio>
            </van-radio-group>
          </view>
          <van-field
            value="{{item.notes}}"
            placeholder="备注说明（可选）"
            type="textarea"
            autosize
            border="{{false}}"
            data-index="{{index}}"
            bind:change="onEquipmentNotesChange"
            custom-style="background: #f8f9fa; border-radius: 8rpx; margin-top: 16rpx;"
          />
        </view>
      </view>
    </view>

    <!-- 问题记录 -->
    <view class="issue-section">
      <view class="section-header">
        <van-icon name="warning-o" size="18px" />
        <text class="section-title">问题记录</text>
        <van-button
          type="primary"
          size="mini"
          bindtap="addIssue"
          class="add-issue-btn">
          添加问题
        </van-button>
      </view>
      <view class="issue-list" wx:if="{{confirmForm.issueList.length > 0}}">
        <view
          class="issue-item"
          wx:for="{{confirmForm.issueList}}"
          wx:key="id">
          <view class="issue-header">
            <van-tag
              type="{{item.severity === 'urgent' ? 'danger' : item.severity === 'major' ? 'warning' : 'default'}}"
              size="small">
              {{item.severity === 'urgent' ? '紧急' : item.severity === 'major' ? '严重' : item.severity === 'normal' ? '一般' : '轻微'}}
            </van-tag>
            <text class="issue-type">{{item.type}}</text>
            <van-icon
              name="cross"
              size="16px"
              data-index="{{index}}"
              bindtap="removeIssue"
            />
          </view>
          <text class="issue-description">{{item.description}}</text>
          <view class="issue-photos" wx:if="{{item.photos.length > 0}}">
            <image
              class="issue-photo"
              wx:for="{{item.photos}}"
              wx:for-item="photo"
              wx:key="*this"
              src="{{photo}}"
              mode="aspectFill"
              bindtap="previewPhoto"
              data-url="{{photo}}"
            />
          </view>
        </view>
      </view>
      <view class="empty-issue" wx:else>
        <van-icon name="records" size="48px" color="#dcdee0" />
        <text class="empty-text">暂无问题记录</text>
      </view>
    </view>

    <!-- 照片附件 -->
    <view class="photo-section">
      <view class="section-header">
        <van-icon name="photo-o" size="18px" />
        <text class="section-title">照片附件</text>
        <van-button
          type="primary"
          size="mini"
          bindtap="addPhoto"
          class="add-photo-btn">
          添加照片
        </van-button>
      </view>
      <view class="photo-grid" wx:if="{{confirmForm.photos.length > 0}}">
        <view
          class="photo-item"
          wx:for="{{confirmForm.photos}}"
          wx:key="*this">
          <image
            class="photo-image"
            src="{{item}}"
            mode="aspectFill"
            bindtap="previewPhoto"
            data-url="{{item}}"
          />
          <view
            class="photo-delete"
            data-index="{{index}}"
            bindtap="removePhoto">
            <van-icon name="cross" size="14px" color="#ffffff" />
          </view>
        </view>
      </view>
      <view class="empty-photos" wx:else>
        <van-icon name="photo-o" size="48px" color="#dcdee0" />
        <text class="empty-text">暂无照片附件</text>
      </view>
    </view>

    <!-- 确认备注 -->
    <view class="notes-section">
      <view class="section-header">
        <van-icon name="edit" size="18px" />
        <text class="section-title">确认备注</text>
      </view>
      <van-field
        value="{{confirmForm.confirmNotes}}"
        placeholder="请输入确认备注（可选）"
        type="textarea"
        autosize
        border="{{false}}"
        bind:change="onConfirmNotesChange"
        custom-style="background: #f8f9fa; border-radius: 12rpx;"
      />
    </view>

    <!-- 电子签名 -->
    <view class="signature-section">
      <view class="section-header">
        <van-icon name="edit" size="18px" />
        <text class="section-title">电子签名</text>
        <text class="required-mark">*</text>
      </view>
      <view class="signature-area" bindtap="openSignature">
        <image
          wx:if="{{confirmForm.receiverSignature}}"
          class="signature-image"
          src="{{confirmForm.receiverSignature}}"
          mode="aspectFit"
        />
        <view wx:else class="signature-placeholder">
          <van-icon name="edit" size="32px" color="#dcdee0" />
          <text class="placeholder-text">点击进行电子签名</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <van-button
        type="default"
        size="large"
        bindtap="rejectHandover"
        disabled="{{submitting}}"
        class="reject-btn">
        拒绝交班
      </van-button>
      <van-button
        type="primary"
        size="large"
        loading="{{submitting}}"
        disabled="{{submitting}}"
        bindtap="submitConfirm"
        class="confirm-btn">
        确认接班
      </van-button>
    </view>
  </view>
</view>

<!-- 添加问题弹窗 -->
<van-popup
  show="{{showIssuePopup}}"
  position="bottom"
  round
  bind:close="closeIssuePopup"
  custom-style="height: 70%;">
  <view class="issue-popup">
    <view class="popup-header">
      <text class="popup-title">添加问题</text>
      <van-icon name="cross" bindtap="closeIssuePopup" />
    </view>
    <scroll-view class="popup-content" scroll-y>
      <view class="form-section">
        <text class="form-label">问题类型 *</text>
        <van-radio-group
          value="{{currentIssue.type}}"
          bind:change="onIssueTypeChange">
          <van-radio
            wx:for="{{issueTypes}}"
            wx:key="value"
            name="{{item.value}}"
            custom-style="margin-bottom: 16rpx;">
            {{item.text}}
          </van-radio>
        </van-radio-group>
      </view>

      <view class="form-section">
        <text class="form-label">严重程度</text>
        <van-radio-group
          value="{{currentIssue.severity}}"
          direction="horizontal"
          bind:change="onIssueSeverityChange">
          <van-radio
            wx:for="{{severityOptions}}"
            wx:key="value"
            name="{{item.value}}">
            {{item.text}}
          </van-radio>
        </van-radio-group>
      </view>

      <view class="form-section">
        <text class="form-label">问题描述 *</text>
        <van-field
          value="{{currentIssue.description}}"
          placeholder="请详细描述问题"
          type="textarea"
          autosize
          border="{{false}}"
          bind:change="onIssueDescriptionChange"
          custom-style="background: #f8f9fa; border-radius: 8rpx;"
        />
      </view>

      <view class="form-section">
        <text class="form-label">相关照片</text>
        <view class="issue-photo-grid">
          <view
            class="issue-photo-item"
            wx:for="{{currentIssue.photos}}"
            wx:key="*this">
            <image
              class="issue-photo-image"
              src="{{item}}"
              mode="aspectFill"
            />
            <view
              class="issue-photo-delete"
              data-index="{{index}}"
              bindtap="removeIssuePhoto">
              <van-icon name="cross" size="12px" color="#ffffff" />
            </view>
          </view>
          <view class="add-issue-photo" bindtap="addIssuePhoto">
            <van-icon name="plus" size="24px" color="#dcdee0" />
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="popup-actions">
      <van-button
        type="default"
        size="large"
        bindtap="closeIssuePopup"
        class="cancel-btn">
        取消
      </van-button>
      <van-button
        type="primary"
        size="large"
        bindtap="confirmAddIssue"
        class="confirm-btn">
        确定
      </van-button>
    </view>
  </view>
</van-popup>

<!-- 全局组件 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
