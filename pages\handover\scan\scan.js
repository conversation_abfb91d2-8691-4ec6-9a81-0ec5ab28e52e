// pages/handover/scan/scan.js
const app = getApp()
const util = require('../../../utils/util.js')
const api = require('../../../utils/api.js')
const config = require('../../../utils/config.js')

Page({
  data: {
    // 扫码状态
    scanning: false,
    scanResult: null,

    // 手动输入
    showManualInput: false,
    manualCode: '',

    // 历史记录
    scanHistory: [],
    showHistory: false,

    // 权限状态
    cameraAuthorized: false
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    // 检查相机权限
    this.checkCameraPermission()
  },

  onHide() {
    // 停止扫码
    this.stopScan()
  },

  // 初始化页面
  async initPage() {
    try {
      // 检查用户权限
      if (!app.checkPermission('nurse')) {
        util.showToast('您没有权限执行此操作')
        wx.navigateBack()
        return
      }

      // 加载扫码历史
      this.loadScanHistory()
    } catch (error) {
      console.error('初始化页面失败:', error)
      util.showToast('页面初始化失败')
    }
  },

  // 检查相机权限
  checkCameraPermission() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.camera'] === false) {
          // 用户拒绝了相机权限
          this.setData({ cameraAuthorized: false })
          this.showPermissionDialog()
        } else if (res.authSetting['scope.camera'] === true) {
          // 用户已授权相机权限
          this.setData({ cameraAuthorized: true })
        } else {
          // 未询问过权限，直接开始扫码
          this.setData({ cameraAuthorized: true })
        }
      }
    })
  },

  // 显示权限对话框
  showPermissionDialog() {
    wx.showModal({
      title: '需要相机权限',
      content: '扫码功能需要使用相机权限，请在设置中开启',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.camera']) {
                this.setData({ cameraAuthorized: true })
                util.showToast('权限已开启')
              }
            }
          })
        } else {
          wx.navigateBack()
        }
      }
    })
  },

  // 开始扫码
  startScan() {
    if (!this.data.cameraAuthorized) {
      this.checkCameraPermission()
      return
    }

    this.setData({ scanning: true })

    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode', 'barCode'],
      success: (res) => {
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        util.showToast('扫码失败，请重试')
      },
      complete: () => {
        this.setData({ scanning: false })
      }
    })
  },

  // 停止扫码
  stopScan() {
    this.setData({ scanning: false })
  },

  // 处理扫码结果
  async handleScanResult(result) {
    try {
      this.setData({ scanResult: result })

      // 保存到历史记录
      this.saveScanHistory(result)

      // 解析二维码内容
      const parseResult = this.parseQRCode(result)

      if (parseResult.type === 'shift') {
        // 交班二维码
        await this.handleShiftQRCode(parseResult.data)
      } else if (parseResult.type === 'equipment') {
        // 设备二维码
        await this.handleEquipmentQRCode(parseResult.data)
      } else {
        // 未知类型
        util.showToast('无法识别的二维码类型')
      }
    } catch (error) {
      console.error('处理扫码结果失败:', error)
      util.showToast('处理扫码结果失败')
    }
  },

  // 解析二维码
  parseQRCode(qrCode) {
    try {
      // 尝试解析JSON格式
      if (qrCode.startsWith('{')) {
        const data = JSON.parse(qrCode)
        return data
      }

      // 解析自定义格式
      if (qrCode.startsWith('shift:')) {
        return {
          type: 'shift',
          data: {
            shiftId: qrCode.replace('shift:', '')
          }
        }
      }

      if (qrCode.startsWith('equipment:')) {
        return {
          type: 'equipment',
          data: {
            equipmentId: qrCode.replace('equipment:', '')
          }
        }
      }

      // 默认处理
      return {
        type: 'unknown',
        data: { content: qrCode }
      }
    } catch (error) {
      console.error('解析二维码失败:', error)
      return {
        type: 'unknown',
        data: { content: qrCode }
      }
    }
  },

  // 处理交班二维码
  async handleShiftQRCode(data) {
    try {
      util.showLoading('验证交班信息...')

      // 验证交班记录
      const shiftDetail = await api.callFunction('getShiftDetail', {
        shiftId: data.shiftId
      })

      util.hideLoading()

      if (shiftDetail.success) {
        // 跳转到交接确认页面
        util.navigateTo('/pages/handover/confirm/confirm', {
          shiftId: data.shiftId
        })
      } else {
        util.showToast(shiftDetail.message || '交班记录不存在')
      }
    } catch (error) {
      util.hideLoading()
      console.error('处理交班二维码失败:', error)
      util.showToast('验证交班信息失败')
    }
  },

  // 处理设备二维码
  async handleEquipmentQRCode(data) {
    try {
      util.showLoading('获取设备信息...')

      // 获取设备详情
      const equipmentDetail = await api.callFunction('getEquipmentDetail', {
        equipmentId: data.equipmentId
      })

      util.hideLoading()

      if (equipmentDetail.success) {
        // 显示设备信息
        this.showEquipmentInfo(equipmentDetail.data)
      } else {
        util.showToast(equipmentDetail.message || '设备不存在')
      }
    } catch (error) {
      util.hideLoading()
      console.error('处理设备二维码失败:', error)
      util.showToast('获取设备信息失败')
    }
  },

  // 显示设备信息
  showEquipmentInfo(equipment) {
    wx.showModal({
      title: equipment.name,
      content: `设备编号：${equipment.code}\n状态：${equipment.status}\n位置：${equipment.location}`,
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 手动输入二维码
  showManualInputDialog() {
    this.setData({
      showManualInput: true,
      manualCode: ''
    })
  },

  // 关闭手动输入
  closeManualInput() {
    this.setData({ showManualInput: false })
  },

  // 手动输入内容变化
  onManualCodeChange(e) {
    this.setData({ manualCode: e.detail })
  },

  // 确认手动输入
  confirmManualInput() {
    const code = this.data.manualCode.trim()
    if (!code) {
      util.showToast('请输入二维码内容')
      return
    }

    this.setData({ showManualInput: false })
    this.handleScanResult(code)
  },

  // 保存扫码历史
  saveScanHistory(result) {
    try {
      let history = wx.getStorageSync('scanHistory') || []

      // 添加新记录
      const newRecord = {
        content: result,
        timestamp: new Date().toISOString(),
        userId: app.globalData.userInfo?.userId
      }

      // 去重并限制数量
      history = history.filter(item => item.content !== result)
      history.unshift(newRecord)
      history = history.slice(0, 50) // 最多保存50条

      wx.setStorageSync('scanHistory', history)
      this.setData({ scanHistory: history })
    } catch (error) {
      console.error('保存扫码历史失败:', error)
    }
  },

  // 加载扫码历史
  loadScanHistory() {
    try {
      const history = wx.getStorageSync('scanHistory') || []
      this.setData({ scanHistory: history })
    } catch (error) {
      console.error('加载扫码历史失败:', error)
    }
  },

  // 显示历史记录
  showScanHistory() {
    this.setData({ showHistory: true })
  },

  // 关闭历史记录
  closeScanHistory() {
    this.setData({ showHistory: false })
  },

  // 选择历史记录
  selectHistoryItem(e) {
    const { index } = e.currentTarget.dataset
    const item = this.data.scanHistory[index]

    this.setData({ showHistory: false })
    this.handleScanResult(item.content)
  },

  // 清空历史记录
  clearScanHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有扫码历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('scanHistory')
          this.setData({ scanHistory: [] })
          util.showToast('已清空历史记录')
        }
      }
    })
  },

  // 重新扫码
  rescan() {
    this.setData({ scanResult: null })
    this.startScan()
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '供应室交班系统 - 扫码接班',
      path: '/pages/handover/scan/scan',
      imageUrl: '/images/share-scan.png'
    }
  }
})
