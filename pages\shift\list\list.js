// pages/shift/list/list.js
const app = getApp()
const util = require('../../../utils/util.js')
const api = require('../../../utils/api.js')
const config = require('../../../utils/config.js')

Page({
  data: {
    // 列表数据
    shiftList: [],
    searchKey: '',
    loading: false,
    loadingMore: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 20,
    
    // 筛选数据
    filterData: {
      status: '',
      shiftType: '',
      priority: '',
      startDate: '',
      endDate: ''
    },
    hasActiveFilters: false,
    showFilterPopup: false,
    
    // 选择器选项
    statusOptions: [
      { text: '待接班', value: 'pending' },
      { text: '已确认', value: 'confirmed' },
      { text: '已拒绝', value: 'rejected' },
      { text: '已取消', value: 'cancelled' },
      { text: '异常', value: 'abnormal' }
    ],
    shiftTypeOptions: [
      { text: '早班', value: 'morning' },
      { text: '中班', value: 'afternoon' },
      { text: '夜班', value: 'night' }
    ],
    priorityOptions: [
      { text: '普通', value: 'normal' },
      { text: '重要', value: 'important' },
      { text: '紧急', value: 'urgent' }
    ],
    
    // 二维码相关
    showQRCodePopup: false,
    qrCodeUrl: '',
    currentShiftId: ''
  },

  onLoad() {
    this.loadShiftList()
  },

  onShow() {
    // 从其他页面返回时刷新列表
    this.refreshList()
  },

  onPullDownRefresh() {
    this.refreshList()
  },

  onReachBottom() {
    this.loadMore()
  },

  // 加载交班列表
  async loadShiftList(isRefresh = false) {
    try {
      if (isRefresh) {
        this.setData({
          currentPage: 1,
          hasMore: true,
          loading: true
        })
      } else if (this.data.loading || this.data.loadingMore) {
        return
      }

      if (!isRefresh && this.data.currentPage === 1) {
        this.setData({ loading: true })
      } else if (!isRefresh) {
        this.setData({ loadingMore: true })
      }

      const userInfo = app.globalData.userInfo
      const params = {
        page: this.data.currentPage,
        limit: this.data.pageSize,
        searchKey: this.data.searchKey,
        ...this.data.filterData
      }

      // 根据用户角色添加筛选条件
      if (userInfo.role === 'nurse') {
        params.creator = userInfo.userId
      } else if (userInfo.role === 'head_nurse') {
        params.department = userInfo.department
      }

      const result = await api.callFunction('getShiftList', params)

      if (result.success) {
        const newList = result.data.list.map(item => ({
          ...item,
          instrumentCount: item.instrumentCheck ? item.instrumentCheck.length : 0,
          equipmentCount: item.equipmentStatus ? item.equipmentStatus.length : 0,
          issueCount: this.calculateIssueCount(item)
        }))

        this.setData({
          shiftList: isRefresh || this.data.currentPage === 1 ? newList : [...this.data.shiftList, ...newList],
          hasMore: result.data.hasMore,
          currentPage: this.data.currentPage + 1
        })
      } else {
        util.showToast('加载失败')
      }
    } catch (error) {
      console.error('加载交班列表失败:', error)
      util.showToast('加载失败')
    } finally {
      this.setData({
        loading: false,
        loadingMore: false
      })
      wx.stopPullDownRefresh()
    }
  },

  // 计算异常数量
  calculateIssueCount(shift) {
    let count = 0
    
    // 检查器械差异
    if (shift.instrumentCheck) {
      count += shift.instrumentCheck.filter(item => item.plan !== item.actual).length
    }
    
    // 检查设备故障
    if (shift.equipmentStatus) {
      count += shift.equipmentStatus.filter(item => item.status === '故障' || item.status === '异常').length
    }
    
    return count
  },

  // 刷新列表
  refreshList() {
    this.setData({ currentPage: 1 })
    this.loadShiftList(true)
  },

  // 加载更多
  loadMore() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadShiftList()
    }
  },

  // 搜索
  onSearch(e) {
    this.setData({
      searchKey: e.detail.value,
      currentPage: 1
    })
    this.loadShiftList(true)
  },

  onSearchChange(e) {
    this.setData({ searchKey: e.detail.value })
  },

  // 显示筛选
  showFilter() {
    this.setData({ showFilterPopup: true })
  },

  closeFilter() {
    this.setData({ showFilterPopup: false })
  },

  // 筛选操作
  selectStatus(e) {
    const value = e.currentTarget.dataset.value
    this.setData({
      'filterData.status': this.data.filterData.status === value ? '' : value
    })
  },

  selectShiftType(e) {
    const value = e.currentTarget.dataset.value
    this.setData({
      'filterData.shiftType': this.data.filterData.shiftType === value ? '' : value
    })
  },

  selectPriority(e) {
    const value = e.currentTarget.dataset.value
    this.setData({
      'filterData.priority': this.data.filterData.priority === value ? '' : value
    })
  },

  // 应用筛选
  applyFilter() {
    const hasFilters = Object.values(this.data.filterData).some(value => value !== '')
    this.setData({
      hasActiveFilters: hasFilters,
      showFilterPopup: false,
      currentPage: 1
    })
    this.loadShiftList(true)
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterData: {
        status: '',
        shiftType: '',
        priority: '',
        startDate: '',
        endDate: ''
      }
    })
  },

  // 清空筛选
  clearAllFilters() {
    this.resetFilter()
    this.setData({
      hasActiveFilters: false,
      currentPage: 1
    })
    this.loadShiftList(true)
  },

  clearStatusFilter() {
    this.setData({ 'filterData.status': '' })
    this.applyFilter()
  },

  clearShiftTypeFilter() {
    this.setData({ 'filterData.shiftType': '' })
    this.applyFilter()
  },

  clearPriorityFilter() {
    this.setData({ 'filterData.priority': '' })
    this.applyFilter()
  },

  // 查看交班详情
  viewShiftDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/shift/detail/detail?id=${id}`
    })
  },

  // 编辑交班
  editShift(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/shift/create/create?id=${id}&mode=edit`
    })
  },

  // 生成二维码
  async generateQRCode(e) {
    try {
      const id = e.currentTarget.dataset.id
      util.showLoading('生成中...')

      const result = await api.callFunction('generateShiftQRCode', { shiftId: id })
      
      if (result.success) {
        this.setData({
          qrCodeUrl: result.data.qrCodeUrl,
          currentShiftId: id,
          showQRCodePopup: true
        })
      } else {
        util.showToast('生成失败')
      }
    } catch (error) {
      console.error('生成二维码失败:', error)
      util.showToast('生成失败')
    } finally {
      util.hideLoading()
    }
  },

  // 关闭二维码
  closeQRCode() {
    this.setData({ showQRCodePopup: false })
  },

  // 保存二维码
  saveQRCode() {
    wx.saveImageToPhotosAlbum({
      filePath: this.data.qrCodeUrl,
      success: () => {
        util.showToast('保存成功', 'success')
      },
      fail: () => {
        util.showToast('保存失败')
      }
    })
  },

  // 分享二维码
  shareQRCode() {
    // 实现分享功能
    util.showToast('分享功能开发中')
  },

  // 新建交班
  createShift() {
    wx.navigateTo({
      url: '/pages/shift/create/create'
    })
  },

  // 工具函数
  getStatusType(status) {
    const statusMap = {
      'pending': 'warning',
      'confirmed': 'success',
      'rejected': 'danger',
      'cancelled': 'default',
      'abnormal': 'danger'
    }
    return statusMap[status] || 'default'
  },

  getStatusText(status) {
    const statusMap = {
      'pending': '待接班',
      'confirmed': '已确认',
      'rejected': '已拒绝',
      'cancelled': '已取消',
      'abnormal': '异常'
    }
    return statusMap[status] || status
  },

  getPriorityText(priority) {
    const priorityMap = {
      'normal': '普通',
      'important': '重要',
      'urgent': '紧急'
    }
    return priorityMap[priority] || priority
  },

  getPriorityColor(priority) {
    const colorMap = {
      'normal': '#1989fa',
      'important': '#ff976a',
      'urgent': '#ee0a24'
    }
    return colorMap[priority] || '#1989fa'
  },

  formatTime(time) {
    return util.formatTime(new Date(time))
  }
})
