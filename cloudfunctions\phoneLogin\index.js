// 云函数：手机号验证码登录
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { phone, verifyCode, action } = event
  const wxContext = cloud.getWXContext()

  try {
    if (action === 'sendCode') {
      // 发送验证码
      return await sendVerifyCode(phone)
    } else if (action === 'login') {
      // 验证码登录
      return await verifyCodeLogin(phone, verifyCode, wxContext)
    } else {
      return {
        success: false,
        message: '无效的操作类型'
      }
    }
  } catch (error) {
    console.error('手机号登录失败:', error)
    return {
      success: false,
      message: '操作失败，请重试',
      error: error.message
    }
  }
}

// 发送验证码
async function sendVerifyCode(phone) {
  try {
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      return {
        success: false,
        message: '请输入正确的手机号'
      }
    }

    // 生成6位验证码
    const code = Math.random().toString().slice(-6)
    const expireTime = new Date(Date.now() + 5 * 60 * 1000) // 5分钟后过期

    // 检查是否频繁发送
    const recentCode = await db.collection('verify_codes')
      .where({
        phone: phone,
        createTime: db.command.gte(new Date(Date.now() - 60 * 1000)) // 1分钟内
      })
      .get()

    if (recentCode.data.length > 0) {
      return {
        success: false,
        message: '验证码发送过于频繁，请稍后再试'
      }
    }

    // 保存验证码到数据库
    await db.collection('verify_codes').add({
      data: {
        phone: phone,
        code: code,
        expireTime: expireTime,
        used: false,
        createTime: new Date()
      }
    })

    // 这里应该调用短信服务发送验证码
    // 由于是演示项目，我们只是模拟发送成功
    console.log(`发送验证码到 ${phone}: ${code}`)

    // 在开发环境下，可以返回验证码用于测试
    const isDev = process.env.NODE_ENV === 'development'
    
    return {
      success: true,
      message: '验证码发送成功',
      data: {
        // 开发环境下返回验证码，生产环境不返回
        code: isDev ? code : undefined,
        expireTime: expireTime
      }
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    throw error
  }
}

// 验证码登录
async function verifyCodeLogin(phone, verifyCode, wxContext) {
  try {
    if (!phone || !verifyCode) {
      return {
        success: false,
        message: '手机号和验证码不能为空'
      }
    }

    // 验证验证码
    const codeQuery = await db.collection('verify_codes')
      .where({
        phone: phone,
        code: verifyCode,
        used: false,
        expireTime: db.command.gte(new Date())
      })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get()

    if (codeQuery.data.length === 0) {
      return {
        success: false,
        message: '验证码错误或已过期'
      }
    }

    // 标记验证码已使用
    await db.collection('verify_codes')
      .doc(codeQuery.data[0]._id)
      .update({
        data: {
          used: true,
          useTime: new Date()
        }
      })

    // 查询用户是否已存在
    const userQuery = await db.collection('users')
      .where({
        phone: phone
      })
      .get()

    let userData

    if (userQuery.data.length > 0) {
      // 用户已存在，更新登录时间
      userData = userQuery.data[0]
      
      await db.collection('users')
        .doc(userData._id)
        .update({
          data: {
            lastLoginTime: new Date(),
            updateTime: new Date()
          }
        })
    } else {
      // 新用户，创建记录
      const createResult = await db.collection('users').add({
        data: {
          openid: wxContext.OPENID,
          unionid: wxContext.UNIONID,
          phone: phone,
          name: '', // 真实姓名，需要后续完善
          nickName: `用户${phone.slice(-4)}`, // 默认昵称
          avatarUrl: '', // 默认头像
          role: 'nurse', // 默认角色为护士
          department: '', // 部门，需要后续设置
          status: 'active', // 用户状态
          createTime: new Date(),
          updateTime: new Date(),
          lastLoginTime: new Date()
        }
      })

      // 获取新创建的用户信息
      const newUserQuery = await db.collection('users')
        .doc(createResult._id)
        .get()
      userData = newUserQuery.data
      userData._id = createResult._id
    }

    // 记录登录日志
    await db.collection('system_logs').add({
      data: {
        type: 'login',
        userId: userData._id,
        openid: wxContext.OPENID,
        action: 'phone_login',
        phone: phone,
        ip: wxContext.CLIENTIP || '',
        userAgent: wxContext.CLIENTIPV6 || '',
        timestamp: new Date()
      }
    })

    return {
      success: true,
      message: '登录成功',
      data: {
        userInfo: {
          userId: userData._id,
          openid: wxContext.OPENID,
          name: userData.name || userData.nickName,
          avatarUrl: userData.avatarUrl,
          role: userData.role,
          department: userData.department,
          phone: userData.phone,
          status: userData.status
        },
        isNewUser: userQuery.data.length === 0
      }
    }
  } catch (error) {
    console.error('验证码登录失败:', error)
    throw error
  }
}
