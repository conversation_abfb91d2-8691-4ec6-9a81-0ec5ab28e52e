// 云函数：获取设备详情
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { equipmentId } = event

  try {
    if (!equipmentId) {
      return {
        success: false,
        message: '设备ID不能为空'
      }
    }

    // 查询设备详情
    const result = await db.collection('instruments')
      .where({
        code: equipmentId
      })
      .get()

    if (result.data.length === 0) {
      return {
        success: false,
        message: '设备不存在'
      }
    }

    const equipment = result.data[0]

    // 格式化设备信息
    const formattedEquipment = {
      _id: equipment._id,
      name: equipment.name,
      code: equipment.code,
      category: equipment.category,
      status: equipment.status,
      location: equipment.location,
      department: equipment.department,
      specifications: equipment.specifications,
      manufacturer: equipment.manufacturer,
      purchaseDate: equipment.purchaseDate,
      warrantyDate: equipment.warrantyDate,
      maintenanceRecord: equipment.maintenanceRecord || [],
      lastCheckTime: equipment.lastCheckTime,
      nextCheckTime: equipment.nextCheckTime,
      responsible: equipment.responsible,
      notes: equipment.notes,
      createTime: equipment.createTime,
      updateTime: equipment.updateTime
    }

    // 获取最近的维护记录
    if (equipment.maintenanceRecord && equipment.maintenanceRecord.length > 0) {
      formattedEquipment.lastMaintenance = equipment.maintenanceRecord
        .sort((a, b) => new Date(b.date) - new Date(a.date))[0]
    }

    // 计算设备状态
    const now = new Date()
    const warrantyDate = new Date(equipment.warrantyDate)
    const nextCheckDate = new Date(equipment.nextCheckTime)

    formattedEquipment.statusInfo = {
      isWarrantyExpired: warrantyDate < now,
      isCheckOverdue: nextCheckDate < now,
      warrantyDaysLeft: Math.ceil((warrantyDate - now) / (1000 * 60 * 60 * 24)),
      checkDaysLeft: Math.ceil((nextCheckDate - now) / (1000 * 60 * 60 * 24))
    }

    return {
      success: true,
      data: formattedEquipment
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    return {
      success: false,
      message: '获取设备详情失败',
      error: error.message
    }
  }
}
