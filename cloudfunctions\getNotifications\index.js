// 云函数：获取通知列表
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { userId, page = 1, pageSize = 20, status, type } = event

  try {
    if (!userId) {
      return {
        success: false,
        message: '用户ID不能为空'
      }
    }

    // 构建查询条件
    let whereCondition = {
      recipients: _.in([userId])
    }

    // 添加状态筛选
    if (status) {
      whereCondition.status = status
    }

    // 添加类型筛选
    if (type) {
      whereCondition.type = type
    }

    // 计算跳过的记录数
    const skip = (page - 1) * pageSize

    // 查询通知列表
    const result = await db.collection('notifications')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get()

    // 查询总数
    const countResult = await db.collection('notifications')
      .where(whereCondition)
      .count()

    // 格式化通知数据
    const notifications = result.data.map(notification => ({
      _id: notification._id,
      type: notification.type,
      title: notification.title,
      content: notification.content,
      data: notification.data,
      priority: notification.priority,
      status: notification.status,
      createTime: notification.createTime,
      readTime: notification.readTime,
      isRead: notification.status === 'read'
    }))

    // 计算分页信息
    const total = countResult.total
    const totalPages = Math.ceil(total / pageSize)
    const hasMore = page < totalPages

    return {
      success: true,
      data: {
        list: notifications,
        pagination: {
          page: page,
          pageSize: pageSize,
          total: total,
          totalPages: totalPages,
          hasMore: hasMore
        }
      }
    }
  } catch (error) {
    console.error('获取通知列表失败:', error)
    return {
      success: false,
      message: '获取通知列表失败',
      error: error.message
    }
  }
}
