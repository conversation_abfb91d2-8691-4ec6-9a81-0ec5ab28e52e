/* pages/message/message.wxss */
.message-container {
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: #ffffff;
  padding: 30rpx 20rpx;
  border-bottom: 2rpx solid #ebedf0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
}

.header-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: #f2f3f5;
  color: #646566;
}

.action-btn.active {
  background: #1989fa;
  color: #ffffff;
}

/* 筛选区域 */
.filter-section {
  background: #ffffff;
  padding: 20rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
}

.filter-tab {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  background: #f2f3f5;
  color: #646566;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #1989fa;
  color: #ffffff;
  transform: scale(1.05);
}

/* 消息列表 */
.message-list {
  padding: 20rpx;
}

.message-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.message-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
}

.message-item.unread {
  border-left: 6rpx solid #1989fa;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
}

.message-item.urgent {
  border-left: 6rpx solid #ee0a24;
  background: linear-gradient(135deg, #ffffff 0%, #fff8f8 100%);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.message-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 20rpx;
  line-height: 1.4;
}

.message-time {
  font-size: 24rpx;
  color: #969799;
  white-space: nowrap;
  background: #f2f3f5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.message-content {
  font-size: 28rpx;
  color: #646566;
  line-height: 1.6;
  margin-bottom: 16rpx;
  max-height: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-type {
  font-size: 24rpx;
  color: #969799;
  background: #f2f3f5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.priority-badge {
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  color: #ffffff;
  font-weight: 600;
}

.priority-urgent {
  background: #ee0a24;
}

.priority-high {
  background: #ff976a;
}

.priority-normal {
  background: #07c160;
}

.unread-dot {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 20rpx;
  height: 20rpx;
  background: linear-gradient(45deg, #ee0a24, #ff4757);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #c8c9cc;
  line-height: 1.6;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  margin-left: 20rpx;
  font-size: 26rpx;
  color: #969799;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 20rpx;
  border-top: 2rpx solid #ebedf0;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-button {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

/* 消息详情弹窗 */
.message-detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.popup-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 30rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #969799;
  margin-bottom: 12rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #323233;
  line-height: 1.6;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .filter-tabs {
    flex-wrap: wrap;
  }

  .message-item {
    padding: 24rpx;
  }

  .message-title {
    font-size: 30rpx;
  }
}
