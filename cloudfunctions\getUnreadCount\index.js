// 云函数：获取未读消息数量
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { userId } = event

  try {
    if (!userId) {
      return {
        success: false,
        message: '用户ID不能为空'
      }
    }

    // 查询未读通知数量
    const unreadNotifications = await db.collection('notifications')
      .where({
        recipients: _.in([userId]),
        status: 'pending'
      })
      .count()

    // 查询待处理的交班数量（分配给该用户的）
    const pendingShifts = await db.collection('shift_records')
      .where({
        status: 'pending',
        // 这里可以根据业务逻辑调整，比如按部门或指定接班人筛选
        department: _.exists(true)
      })
      .count()

    // 查询用户创建的被拒绝的交班
    const rejectedShifts = await db.collection('shift_records')
      .where({
        creator: userId,
        status: 'rejected'
      })
      .count()

    return {
      success: true,
      data: {
        notifications: unreadNotifications.total,
        pendingShifts: pendingShifts.total,
        rejectedShifts: rejectedShifts.total,
        total: unreadNotifications.total + rejectedShifts.total
      }
    }
  } catch (error) {
    console.error('获取未读数量失败:', error)
    return {
      success: false,
      message: '获取未读数量失败',
      error: error.message
    }
  }
}
