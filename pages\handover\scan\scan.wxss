/* pages/handover/scan/scan.wxss */
.scan-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
}

.scan-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-actions {
  display: flex;
  gap: 20rpx;
  color: #ffffff;
}

.scan-content {
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scan-area {
  position: relative;
  width: 500rpx;
  height: 500rpx;
  margin-bottom: 60rpx;
}

.scan-frame {
  position: relative;
  width: 100%;
  height: 100%;
  border: 4rpx solid rgba(25, 137, 250, 0.3);
  border-radius: 20rpx;
  overflow: hidden;
}

.scan-corner {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #1989fa;
}

.scan-corner-tl {
  top: -6rpx;
  left: -6rpx;
  border-right: none;
  border-bottom: none;
  border-top-left-radius: 20rpx;
}

.scan-corner-tr {
  top: -6rpx;
  right: -6rpx;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 20rpx;
}

.scan-corner-bl {
  bottom: -6rpx;
  left: -6rpx;
  border-right: none;
  border-top: none;
  border-bottom-left-radius: 20rpx;
}

.scan-corner-br {
  bottom: -6rpx;
  right: -6rpx;
  border-left: none;
  border-top: none;
  border-bottom-right-radius: 20rpx;
}

.scan-line {
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 4rpx;
  background: linear-gradient(90deg, transparent 0%, #1989fa 50%, transparent 100%);
  animation: scan-line 2s linear infinite;
}

@keyframes scan-line {
  0% {
    transform: translateY(-250rpx);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(250rpx);
    opacity: 0;
  }
}

.scan-tips {
  position: absolute;
  bottom: -80rpx;
  left: 0;
  right: 0;
  text-align: center;
}

.tips-text {
  font-size: 28rpx;
  color: #646566;
  line-height: 1.5;
}

.scan-actions {
  width: 100%;
  max-width: 600rpx;
}

.scan-btn {
  width: 100%;
  margin-bottom: 30rpx;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.action-row {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.manual-btn,
.history-btn {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
}

.permission-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 40rpx;
  background: #fff7e6;
  border-radius: 16rpx;
  margin: 40rpx 0;
}

.permission-btn {
  width: 200rpx;
  height: 60rpx;
}

.scan-result {
  width: 100%;
  max-width: 600rpx;
  margin-top: 40rpx;
}

.result-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.result-content {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.result-text {
  font-size: 28rpx;
  color: #646566;
  word-break: break-all;
  line-height: 1.6;
}

.result-actions {
  text-align: center;
}

.rescan-btn {
  width: 200rpx;
  height: 60rpx;
}

.scan-guide {
  width: 100%;
  max-width: 600rpx;
  margin-top: 60rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.guide-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #323233;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.guide-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.guide-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: #1989fa;
  color: #ffffff;
  border-radius: 50%;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.guide-text {
  font-size: 28rpx;
  color: #646566;
  line-height: 1.6;
  flex: 1;
}

/* 弹窗样式 */
.manual-input-popup,
.history-popup {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #ebedf0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.popup-content {
  padding: 32rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  padding: 32rpx;
  border-top: 2rpx solid #ebedf0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
}

.history-list {
  max-height: 800rpx;
  padding: 0 32rpx 32rpx;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f7f8fa;
}

.history-item:last-child {
  border-bottom: none;
}

.history-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-text {
  font-size: 28rpx;
  color: #323233;
  word-break: break-all;
  line-height: 1.4;
}

.history-time {
  font-size: 24rpx;
  color: #969799;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 80rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .scan-area {
    width: 400rpx;
    height: 400rpx;
  }

  .scan-corner {
    width: 50rpx;
    height: 50rpx;
  }
}
