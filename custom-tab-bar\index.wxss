/* custom-tab-bar/index.wxss */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  border-top: 2rpx solid #ebedf0;
  padding: 12rpx 0;
  padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  color: #7A7E83;
  font-size: 24rpx;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 16rpx;
  margin: 0 8rpx;
}

.tab-item:active {
  transform: scale(0.95);
}

.tab-item-active {
  color: #1989fa;
  background: linear-gradient(135deg, #e8f4ff 0%, #f0f9ff 100%);
  transform: translateY(-2rpx);
}

.tab-item-active::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #1989fa 0%, #1976d2 100%);
  border-radius: 2rpx;
}

.tab-icon {
  font-size: 44rpx;
  margin-bottom: 6rpx;
  transition: all 0.3s ease;
}

.tab-item-active .tab-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 2rpx 8rpx rgba(25, 137, 250, 0.3));
}

.tab-text {
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-item-active .tab-text {
  font-weight: 600;
  transform: scale(1.05);
}

/* 消息角标 */
.tab-badge {
  position: absolute;
  top: 4rpx;
  right: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: linear-gradient(45deg, #ee0a24, #ff4757);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  color: #ffffff;
  font-weight: 600;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(238, 10, 36, 0.3);
  animation: bounce 2s infinite;
}

.tab-badge.dot {
  min-width: 16rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4rpx);
  }
  60% {
    transform: translateY(-2rpx);
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .tab-icon {
    font-size: 40rpx;
  }

  .tab-text {
    font-size: 18rpx;
  }

  .tab-badge {
    min-width: 28rpx;
    height: 28rpx;
    font-size: 16rpx;
  }
}
