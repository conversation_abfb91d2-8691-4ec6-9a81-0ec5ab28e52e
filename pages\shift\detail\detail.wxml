<!--pages/shift/detail/detail.wxml-->
<view class="detail-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#1989fa" vertical>
    加载中...
  </van-loading>

  <!-- 详情内容 -->
  <view wx:else class="detail-content">
    <!-- 基本信息 -->
    <van-cell-group title="基本信息" inset>
      <van-cell title="交班类型" value="{{shiftDetail.shiftType}}" />
      <van-cell title="交班日期" value="{{shiftDetail.shiftDate}}" />
      <van-cell title="创建人" value="{{shiftDetail.creatorName}}" />
      <van-cell title="科室" value="{{shiftDetail.department}}" />
      <van-cell title="优先级" value="{{getPriorityText(shiftDetail.priority)}}" />
      <van-cell title="状态">
        <van-tag slot="right-icon" type="primary">{{getStatusText(shiftDetail.status)}}</van-tag>
      </van-cell>
      <van-cell title="创建时间" value="{{formatTime(shiftDetail.createTime)}}" />
    </van-cell-group>

    <!-- 器械检查 -->
    <van-cell-group title="器械检查" inset wx:if="{{shiftDetail.instrumentCheck.length > 0}}">
      <van-cell 
        wx:for="{{shiftDetail.instrumentCheck}}" 
        wx:key="index"
        title="{{item.name}}"
        label="计划: {{item.plan}}{{item.unit}} | 实际: {{item.actual}}{{item.unit}}"
        value="{{item.plan === item.actual ? '正常' : '异常'}}"
        value-class="{{item.plan === item.actual ? 'status-normal' : 'status-error'}}"
      />
    </van-cell-group>

    <!-- 设备状态 -->
    <van-cell-group title="设备状态" inset wx:if="{{shiftDetail.equipmentStatus.length > 0}}">
      <van-cell 
        wx:for="{{shiftDetail.equipmentStatus}}" 
        wx:key="index"
        title="{{item.name}}"
        label="{{item.location}}"
        value="{{item.status}}"
        value-class="{{item.status === '正常' ? 'status-normal' : 'status-error'}}"
      />
    </van-cell-group>

    <!-- 特殊说明 -->
    <van-cell-group title="特殊说明" inset wx:if="{{shiftDetail.specialNotes}}">
      <van-cell>
        <view class="notes-content">{{shiftDetail.specialNotes}}</view>
      </van-cell>
    </van-cell-group>

    <!-- 附件 -->
    <van-cell-group title="附件" inset wx:if="{{shiftDetail.attachments.length > 0}}">
      <view class="attachments">
        <view 
          class="attachment-item" 
          wx:for="{{shiftDetail.attachments}}" 
          wx:key="index"
          bindtap="previewAttachment"
          data-url="{{item.url}}"
          data-type="{{item.type}}">
          <van-image 
            wx:if="{{item.type === 'image'}}"
            src="{{item.url}}" 
            width="100rpx" 
            height="100rpx" 
            fit="cover" 
            round />
          <view wx:else class="file-icon">
            <van-icon name="description" size="40rpx" />
            <text class="file-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </van-cell-group>

    <!-- 交班人操作按钮 -->
    <view class="action-buttons" wx:if="{{shiftDetail.status === 'pending' && userInfo.userId === shiftDetail.creator}}">
      <van-button type="default" size="large" bindtap="editShift">编辑交班</van-button>
      <van-button type="primary" size="large" bindtap="generateQRCode">生成二维码</van-button>
      <van-button type="danger" size="large" bindtap="cancelShift">取消交班</van-button>
    </view>

    <!-- 接班人操作按钮 -->
    <view class="action-buttons" wx:if="{{shiftDetail.status === 'pending' && userInfo.userId !== shiftDetail.creator}}">
      <van-button type="primary" size="large" bindtap="confirmHandover">确认接班</van-button>
      <van-button type="default" size="large" bindtap="rejectHandover">拒绝接班</van-button>
      <van-button type="info" size="large" bindtap="showQRCode" wx:if="{{shiftDetail.qrCode}}">查看二维码</van-button>
    </view>

    <!-- 已完成状态显示 -->
    <view class="status-info" wx:if="{{shiftDetail.status === 'confirmed'}}">
      <van-cell-group>
        <van-cell title="接班人" value="{{shiftDetail.handoverUserName}}" />
        <van-cell title="接班时间" value="{{formatTime(shiftDetail.handoverTime)}}" />
        <van-cell title="状态" value="已确认接班" label-class="status-confirmed" />
      </van-cell-group>
    </view>

    <!-- 已拒绝状态显示 -->
    <view class="status-info" wx:if="{{shiftDetail.status === 'rejected'}}">
      <van-cell-group>
        <van-cell title="拒绝人" value="{{shiftDetail.rejectUserName}}" />
        <van-cell title="拒绝时间" value="{{formatTime(shiftDetail.rejectTime)}}" />
        <van-cell title="拒绝原因" value="{{shiftDetail.rejectReason}}" />
        <van-cell title="状态" value="已拒绝" label-class="status-rejected" />
      </van-cell-group>
    </view>
  </view>
</view>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Dialog组件 -->
<van-dialog id="van-dialog" />
